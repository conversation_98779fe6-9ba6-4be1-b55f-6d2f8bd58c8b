Abs
Acos
Acosh
Add
AddV2
Angle
AnonymousRandomSeedGenerator
AnonymousSeedGenerator
ArgMax
ArgMin
Asin
Asinh
Assert
Atan
Atan2
Atanh
AudioSummary
AudioSummaryV2
BandedTriangularSolve
Barrier
BarrierClose
BarrierIncompleteSize
BarrierInsertMany
BarrierReadySize
BarrierTakeMany
Batch
BatchFunction
BatchMatrixSetDiag
BatchNormWithGlobalNormalization
BatchNormWithGlobalNormalizationGrad
BesselI0
BesselI0e
BesselI1
BesselI1e
BesselJ0
BesselJ1
BesselK0
BesselK0e
BesselK1
BesselK1e
BesselY0
BesselY1
Betainc
BitwiseAnd
BitwiseOr
BitwiseXor
BlockLSTM
BlockLSTMGrad
BlockLSTMGradV2
BlockLSTMV2
BoostedTreesAggregateStats
BoostedTreesCalculateBestFeatureSplit
BoostedTreesCalculateBestFeatureSplitV2
BoostedTreesCalculateBestGainsPerFeature
BoostedTreesCenterBias
BoostedTreesCreateEnsemble
BoostedTreesDeserializeEnsemble
BoostedTreesExampleDebugOutputs
BoostedTreesGetEnsembleStates
BoostedTreesMakeStatsSummary
BoostedTreesPredict
BoostedTreesSerializeEnsemble
BoostedTreesSparseAggregateStats
BoostedTreesSparseCalculateBestFeatureSplit
BoostedTreesTrainingPredict
BoostedTreesUpdateEnsemble
BoostedTreesUpdateEnsembleV2
BroadcastTo
Bucketize
BytesProducedStatsDataset
CacheDataset
CacheDatasetV2
Ceil
ChooseFastestBranchDataset
ChooseFastestDataset
ClipByValue
ComplexAbs
Conj
Conv2DBackpropFilter
Conv3D
Conv3DBackpropFilter
Conv3DBackpropFilterV2
Conv3DBackpropInput
Conv3DBackpropInputV2
Copy
CopyHost
Cos
Cosh
CropAndResize
CropAndResizeGradBoxes
CropAndResizeGradImage
Cumprod
Cumsum
CumulativeLogsumexp
DataServiceDataset
DataServiceDatasetV2
DataServiceDatasetV3
DatasetCardinality
DatasetFromGraph
DatasetToGraph
DatasetToGraphV2
Dawsn
DebugIdentity
DebugIdentityV2
DebugNanCount
DebugNumericSummary
DebugNumericSummaryV2
DecodePaddedRaw
DeleteRandomSeedGenerator
DeleteSeedGenerator
DenseToSparseBatchDataset
DepthwiseConv2dNative
Digamma
Dilation2D
Dilation2DBackpropFilter
Dilation2DBackpropInput
DirectedInterleaveDataset
DivNoNan
DrawBoundingBoxes
DrawBoundingBoxesV2
DummyIterationCounter
DummySeedGenerator
Eig
Elu
EluGrad
EncodeWav
Erf
Erfc
Exp
ExperimentalBytesProducedStatsDataset
ExperimentalChooseFastestDataset
ExperimentalDatasetCardinality
ExperimentalDenseToSparseBatchDataset
ExperimentalDirectedInterleaveDataset
ExperimentalLatencyStatsDataset
ExperimentalTakeWhileDataset
ExperimentalUniqueDataset
Expint
Expm1
ExtractVolumePatches
FFT
FFT2D
FFT3D
FIFOQueue
FIFOQueueV2
FileSystemSetConfiguration
Floor
FractionalMaxPool
FractionalMaxPoolGrad
FresnelCos
FresnelSin
FusedBatchNorm
FusedBatchNormGrad
FusedBatchNormGradV2
FusedBatchNormGradV3
FusedBatchNormV2
FusedBatchNormV3
GRUBlockCell
GRUBlockCellGrad
GetElementAtIndex
Greater
HostConst
IFFT
IFFT2D
IFFT3D
IRFFT
IRFFT2D
IRFFT3D
Igamma
IgammaGradA
Igammac
Imag
ImmutableConst
InTopK
InTopKV2
Inv
InvGrad
Invert
IsBoostedTreesEnsembleInitialized
IsFinite
IsInf
IsNan
L2Loss
LSTMBlockCell
LSTMBlockCellGrad
LatencyStatsDataset
LeakyRelu
LeakyReluGrad
LeftShift
Lgamma
LinSpace
Log
Log1p
LogSoftmax
LogicalNot
LogicalOr
Lu
MapClear
MapIncompleteSize
MapPeek
MapSize
MapStage
MapUnstage
MapUnstageNoKey
MatchingFiles
MatrixSetDiag
MatrixSetDiagV2
MatrixSetDiagV3
MatrixSolve
MatrixSquareRoot
MaxPool
MaxPoolGrad
MaxPoolGradGrad
MaxPoolGradGradV2
MaxPoolGradV2
MaxPoolGradWithArgmax
MaxPoolV2
MaxPoolWithArgmax
Mfcc
ModelDataset
Mul
MulNoNan
Ndtri
Neg
NegTrain
NextAfter
NonDeterministicInts
OptionsDataset
OrderedMapClear
OrderedMapIncompleteSize
OrderedMapPeek
OrderedMapSize
OrderedMapStage
OrderedMapUnstage
OrderedMapUnstageNoKey
Pad
PadV2
ParallelBatchDataset
Polygamma
Pow
Print
PrintV2
QuantizeDownAndShrinkRange
QuantizedConv2D
QuantizedResizeBilinear
RFFT
RFFT2D
RFFT3D
RaggedCross
RaggedTensorFromVariant
RaggedTensorToVariant
RaggedTensorToVariantGradient
RandomCrop
RandomGammaGrad
Range
ReadFile
Real
RealDiv
Reciprocal
ReciprocalGrad
RecordInput
Relu
Relu6
Relu6Grad
ReluGrad
RepeatDataset
Reshape
ResourceStridedSliceAssign
RightShift
Rint
RngReadAndSkip
RngSkip
Round
Rsqrt
RsqrtGrad
SampleDistortedBoundingBox
SampleDistortedBoundingBoxV2
ScatterAdd
ScatterDiv
ScatterMax
ScatterMin
ScatterMul
ScatterSub
ScatterUpdate
Selu
SeluGrad
SerializeManySparse
SerializeSparse
ShuffleAndRepeatDataset
ShuffleAndRepeatDatasetV2
ShuffleDataset
ShuffleDatasetV2
ShuffleDatasetV3
Sigmoid
SigmoidGrad
Sign
Sin
Sinh
Skipgram
Softmax
Softplus
SoftplusGrad
SpaceToBatch
SpaceToBatchND
SpaceToDepth
SparseAdd
SparseConcat
SparseDenseCwiseAdd
SparseDenseCwiseDiv
SparseDenseCwiseMul
SparseFillEmptyRows
SparseFillEmptyRowsGrad
SparseMatMul
SparseMatrixOrderingAMD
SparseMatrixSparseCholesky
SparseMatrixTranspose
SparseSoftmaxCrossEntropyWithLogits
SparseTensorDenseMatMul
Spence
Sqrt
SqrtGrad
Square
Stack
StackClose
StackCloseV2
StackPop
StackPopV2
StackPush
StackPushV2
StackV2
Stage
StageClear
StagePeek
StageSize
StatefulRandomBinomial
StatefulStandardNormal
StatefulStandardNormalV2
StatefulTruncatedNormal
StatefulUniform
StatefulUniformFullInt
StatefulUniformInt
StatelessRandomBinomial
StatelessRandomGammaV2
StatelessSampleDistortedBoundingBox
StridedSlice
StridedSliceAssign
StridedSliceGrad
StringJoin
StringLength
StringNGrams
StringStrip
StringToNumber
Substr
TFRecordReader
TFRecordReaderV2
TakeWhileDataset
Tan
Tanh
TanhGrad
TensorListConcat
TensorListConcatV2
TensorListFromTensor
TensorListGather
TensorListGetItem
TensorListPopBack
TensorListPushBackBatch
TensorListScatter
TensorListScatterIntoExistingList
TensorListScatterV2
TensorListSplit
TensorListStack
TensorStridedSliceUpdate
TextLineReader
TextLineReaderV2
Timestamp
TopK
TopKV2
TridiagonalMatMul
TruncateDiv
Unbatch
UnbatchGrad
Unique
UniqueDataset
UniqueV2
UniqueWithCounts
UniqueWithCountsV2
UnsortedSegmentMax
UnsortedSegmentMin
UnsortedSegmentProd
UnsortedSegmentSum
Unstage
Where
WholeFileReader
WholeFileReaderV2
WindowDataset
WriteFile
Xdivy
Xlog1py
Xlogy
Zeta
