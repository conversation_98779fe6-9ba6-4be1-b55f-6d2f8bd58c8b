# TensorScope

----



🤖 [Demo website](https://sproutai.streamlit.app/)

This is a testing tool named TensorScope to generate test cases to test cross deep learning framework APIs. Please read the [paper](./tensorscope_sec23.pdf) for the details.


Here are the bugs found by Tensors<PERSON>: [Buglist](https://docs.google.com/spreadsheets/d/1F6FjI02LLfJjksdtcWdzo8P_7Ra-cELpF6I3GTvs-hc/edit#gid=0)

## Cite

If you use TensorScope in your work, consider citing our paper presented at USENIX Sec'23 :)

```bibtex
@inproceedings{deng2023differential,
  title={Differential Testing of Cross Deep Learning Framework $\{$APIs$\}$: Revealing Inconsistencies and Vulnerabilities},
  author={Deng, Zizhuang and <PERSON>g, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={32nd USENIX Security Symposium (USENIX Security 23)},
  pages={7393--7410},
  year={2023}
}
```
