{"onnx::Abs": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Acos": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Acosh": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Add": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Asin": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Asinh": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Atan": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Atanh": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::AveragePool": {"ops": ["nn.AvgPool1d", "nn.AvgPool2d", "nn.AvgPool3D"], "hard_template": "False"}, "onnx::BatchNormalization": {"ops": ["nn.BatchNorm1d", "nn.BatchNorm2d", "nn.BatchNorm3d"], "hard_template": "False"}, "onnx::Cast": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Ceil": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Clip": {"ops": ["P.clip_by_value"], "hard_template": "False"}, "onnx::Concat": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Conv": {"ops": ["nn.Conv1d", "nn.Conv2d", "nn.Conv3d"], "hard_template": "False"}, "onnx::ConvTranspose": {"ops": ["nn.Conv3dTranspose", "P.Conv2DBackpropInput"], "hard_template": "False"}, "onnx::Cos": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "onnx::CumSum": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Div": {"ops": ["P<PERSON>Div"], "hard_template": "False"}, "onnx::Dropout": {"ops": ["nn.Dropout"], "hard_template": "False"}, "onnx::Einsum": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Erf": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Exp": {"ops": ["P.Exp"], "hard_template": "False"}, "onnx::Flatten": {"ops": ["nn.<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Floor": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Gather": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Gemm": {"ops": ["nn.<PERSON><PERSON>"], "hard_template": "False"}, "onnx::GlobalAveragePool": {"ops": ["nn.AvgPool1d", "nn.AvgPool2d"], "hard_template": "False"}, "onnx::Greater": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "onnx::LSTM": {"ops": ["nn.LSTM"], "hard_template": "False"}, "onnx::LeakyRelu": {"ops": ["nn.LeakyReLU"], "hard_template": "False"}, "onnx::Log": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::MatMul": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::MaxPool": {"ops": ["nn.<PERSON>Pool1d", "nn.<PERSON>Pool2d", "P.<PERSON>3D"], "hard_template": "False"}, "onnx::Mul": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Neg": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Not": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::OneHot": {"ops": ["nn.OneHot"], "hard_template": "False"}, "onnx::PRelu": {"ops": ["nn.PReLU"], "hard_template": "False"}, "onnx::Pad": {"ops": ["nn.Pad"], "hard_template": "False"}, "onnx::Pow": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Reciprocal": {"ops": ["<PERSON><PERSON>Reciprocal"], "hard_template": "False"}, "onnx::ReduceMean": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::ReduceSum": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Relu": {"ops": ["nn.ReLU6", "nn.ReLU"], "hard_template": "False"}, "onnx::Reshape": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Resize": {"ops": ["P.<PERSON>ili<PERSON>r"], "hard_template": "False"}, "onnx::Rsqrt": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Sigmoid": {"ops": ["nn.<PERSON><PERSON><PERSON><PERSON>"], "hard_template": "False"}, "onnx::Sin": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Slice": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Softmax": {"ops": ["nn.Softmax"], "hard_template": "False"}, "onnx::Split": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "onnx::Sqrt": {"ops": ["P.Sqrt"], "hard_template": "False"}, "onnx::Squeeze": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Sub": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "onnx::Tanh": {"ops": ["nn.<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Transpose": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "onnx::Unsqueeze": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "onnx::Where": {"ops": ["P.<PERSON>"], "hard_template": "False"}}