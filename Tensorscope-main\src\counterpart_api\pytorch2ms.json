{"aten::abs": {"ops": ["absolute"], "hard_template": "False"}, "aten::add_": {"ops": ["add_"], "hard_template": "False"}, "aten::add": {"ops": ["add"], "hard_template": "False"}, "aten::addmm": {"ops": ["Addmm"], "hard_template": "False"}, "aten::_convolution": {"ops": ["nn.Conv1d", "nn.Conv2d", "nn.Conv3d"], "hard_template": "False"}, "aten::cat": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::chunk": {"ops": ["chunk"], "hard_template": "False"}, "aten::clamp": {"ops": ["P.clip_by_value"], "hard_template": "False"}, "aten::clone": {"ops": ["clone"], "hard_template": "False"}, "aten::copy_": {"ops": ["copy"], "hard_template": "False"}, "aten::copy": {"ops": ["copy"], "hard_template": "False"}, "aten::cos": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::cumsum": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::constant_pad_nd": {"ops": ["nn.Pad"], "hard_template": "False"}, "aten::detach": {"ops": ["RemovedOp"], "hard_template": "False"}, "aten::div_": {"ops": ["P<PERSON>Div"], "hard_template": "False"}, "aten::div": {"ops": ["P<PERSON>Div"], "hard_template": "False"}, "aten::dropout": {"ops": ["nn.Dropout"], "hard_template": "False"}, "aten::embedding": {"ops": ["nn.Embedding"], "hard_template": "False"}, "aten::empty": {"ops": ["empty"], "hard_template": "False"}, "aten::eq": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::exp": {"ops": ["P.Exp"], "hard_template": "False"}, "aten::expand_as": {"ops": ["expand_as"], "hard_template": "False"}, "aten::expand": {"ops": ["broadcast_to"], "hard_template": "False"}, "aten::flatten": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::fill_": {"ops": ["fill"], "hard_template": "False"}, "aten::fill": {"ops": ["fill"], "hard_template": "False"}, "aten::floor_divide": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::full_like": {"ops": ["full_like"], "hard_template": "False"}, "aten::full": {"ops": ["full"], "hard_template": "False"}, "aten::gelu": {"ops": ["<PERSON><PERSON>()", "<PERSON><PERSON>()"], "hard_template": "True"}, "aten::gt": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::im2col": {"ops": ["<PERSON><PERSON>()", "nn.Pad()", "nn.Unfold()", "<PERSON><PERSON>()"], "hard_template": "True"}, "aten::index_select": {"ops": ["index_select"], "hard_template": "False"}, "aten::index": {"ops": ["index"], "hard_template": "False"}, "aten::layer_norm": {"ops": ["nn.LayerNorm"], "hard_template": "False"}, "aten::le": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::log_softmax": {"ops": ["nn.LogSoftmax"], "hard_template": "False"}, "aten::log": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::lt": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::masked_fill_": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::masked_fill": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::matmul": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::mean": {"ops": ["mean"], "hard_template": "False"}, "aten::min": {"ops": ["<PERSON><PERSON>Minimum"], "hard_template": "False"}, "aten::mul_": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::mul": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::multiply": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::max_pool1d": {"ops": ["name"], "hard_template": "False"}, "aten::ne": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::neg": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::new_ones": {"ops": ["new_ones"], "hard_template": "False"}, "aten::new_zeros": {"ops": ["new_zeros"], "hard_template": "False"}, "aten::nll_loss": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::ones_like": {"ops": ["ones_like"], "hard_template": "False"}, "aten::ones": {"ops": ["ones"], "hard_template": "False"}, "aten::permute": {"ops": ["transpose"], "hard_template": "False"}, "aten::pow": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::reciprocal": {"ops": ["<PERSON><PERSON>Reciprocal"], "hard_template": "False"}, "aten::relu": {"ops": ["nn.ReLU"], "hard_template": "False"}, "aten::repeat_interleave": {"ops": ["repeat"], "hard_template": "False"}, "aten::repeat": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::reshape_as": {"ops": ["reshape_as"], "hard_template": "False"}, "aten::reshape": {"ops": ["reshape"], "hard_template": "False"}, "aten::rsqrt": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::rsub": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::select": {"ops": ["PrimOp"], "hard_template": "False"}, "aten::size": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::slice": {"ops": ["prim_slice"], "hard_template": "False"}, "aten::softmax": {"ops": ["nn.Softmax"], "hard_template": "False"}, "aten::split": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::squeeze": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::stack": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::sub": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::sum": {"ops": ["sum"], "hard_template": "False"}, "aten::t": {"ops": ["transpose"], "hard_template": "False"}, "aten::tanh": {"ops": ["nn.<PERSON><PERSON>"], "hard_template": "False"}, "aten::to": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::transpose": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::type_as": {"ops": ["<PERSON><PERSON>"], "hard_template": "False"}, "aten::unsqueeze": {"ops": ["P.<PERSON>"], "hard_template": "False"}, "aten::view": {"ops": ["view"], "hard_template": "False"}, "aten::where": {"ops": ["where"], "hard_template": "False"}, "aten::zeros_like": {"ops": ["zeros_like"], "hard_template": "False"}, "aten::zeros": {"ops": ["zeros"], "hard_template": "False"}}